import { useEffect, useState } from 'react';
import { Clipboard, AppState } from 'react-native';

export function useClipboard() {
  const [clipboardContent, setClipboardContent] = useState<string>('');
  const [lastProcessedContent, setLastProcessedContent] = useState<string>('');

  const isValidUrl = (string: string): boolean => {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch {
      return false;
    }
  };

  const checkClipboard = async () => {
    try {
      const content = await Clipboard.getString();
      if (content && content !== lastProcessedContent && isValidUrl(content)) {
        setClipboardContent(content);
        return content;
      }
    } catch (error) {
      console.log('Error reading clipboard:', error);
    }
    return null;
  };

  const markContentAsProcessed = (content: string) => {
    setLastProcessedContent(content);
  };

  useEffect(() => {
    // Check clipboard when app becomes active
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        checkClipboard();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    // Initial check
    checkClipboard();

    return () => subscription?.remove();
  }, [lastProcessedContent]);

  return {
    clipboardContent,
    checkClipboard,
    markContentAsProcessed,
    isValidUrl,
  };
}