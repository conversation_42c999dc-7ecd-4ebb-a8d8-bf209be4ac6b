export interface SavedLink {
  id: string;
  url: string;
  title: string;
  description: string;
  category: LinkCategory;
  tags: string[];
  thumbnail?: string;
  isFavorite: boolean;
  accessCount: number;
  createdAt: Date;
  lastAccessedAt?: Date;
}

export type LinkCategory = 'all' | 'work' | 'personal' | 'social' | 'shopping' | 'custom';

export interface LinkFormData {
  url: string;
  title: string;
  description: string;
  category: LinkCategory;
  tags: string[];
}