import React, { useState, useEffect } from 'react';
import { View, FlatList, StyleSheet, SafeAreaView, RefreshControl } from 'react-native';
import { Plus } from 'lucide-react-native';
import { useLinks } from '@/hooks/useLinks';
import { useClipboard } from '@/hooks/useClipboard';
import { useTheme } from '@/hooks/useTheme';
import { LinkCard } from '@/components/LinkCard';
import { LinkFormModal } from '@/components/LinkFormModal';
import { ClipboardPrompt } from '@/components/ClipboardPrompt';
import { SearchBar } from '@/components/SearchBar';
import { EmptyState } from '@/components/EmptyState';
import { SavedLink, LinkFormData } from '@/types/link';
import { TouchableOpacity } from 'react-native';

export default function AllLinksScreen() {
  const theme = useTheme();
  const {
    links,
    loading,
    addLink,
    updateLink,
    deleteLink,
    toggleFavorite,
    incrementAccessCount,
    searchLinks,
  } = useLinks();
  
  const { clipboardContent, markContentAsProcessed } = useClipboard();
  
  const [showLinkForm, setShowLinkForm] = useState(false);
  const [showClipboardPrompt, setShowClipboardPrompt] = useState(false);
  const [editingLink, setEditingLink] = useState<SavedLink | null>(null);
  const [pendingUrl, setPendingUrl] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Show clipboard prompt when new link is detected
  useEffect(() => {
    if (clipboardContent && !showClipboardPrompt && !showLinkForm) {
      setShowClipboardPrompt(true);
      setPendingUrl(clipboardContent);
    }
  }, [clipboardContent, showClipboardPrompt, showLinkForm]);

  const handleClipboardAccept = () => {
    setShowClipboardPrompt(false);
    setShowLinkForm(true);
    markContentAsProcessed(clipboardContent);
  };

  const handleClipboardDismiss = () => {
    setShowClipboardPrompt(false);
    markContentAsProcessed(clipboardContent);
    setPendingUrl('');
  };

  const handleSaveLink = async (data: LinkFormData) => {
    if (editingLink) {
      await updateLink(editingLink.id, data);
    } else {
      await addLink(data);
    }
    setShowLinkForm(false);
    setEditingLink(null);
    setPendingUrl('');
  };

  const handleEditLink = (link: SavedLink) => {
    setEditingLink(link);
    setShowLinkForm(true);
  };

  const handleCloseForm = () => {
    setShowLinkForm(false);
    setEditingLink(null);
    setPendingUrl('');
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const filteredLinks = searchQuery ? searchLinks(searchQuery) : links;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    fab: {
      position: 'absolute',
      right: theme.spacing.lg,
      bottom: theme.spacing.lg,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <SearchBar onSearch={setSearchQuery} />
        
        <FlatList
          data={filteredLinks}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <LinkCard
              link={item}
              onToggleFavorite={toggleFavorite}
              onDelete={deleteLink}
              onEdit={handleEditLink}
              onAccess={incrementAccessCount}
            />
          )}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={
            <EmptyState 
              type={searchQuery ? 'no-search' : 'no-links'}
            />
          }
        />
      </View>

      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowLinkForm(true)}
        activeOpacity={0.8}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>

      <LinkFormModal
        visible={showLinkForm}
        onClose={handleCloseForm}
        onSave={handleSaveLink}
        initialData={editingLink}
        initialUrl={pendingUrl}
      />

      <ClipboardPrompt
        visible={showClipboardPrompt}
        url={pendingUrl}
        onAccept={handleClipboardAccept}
        onDismiss={handleClipboardDismiss}
      />
    </SafeAreaView>
  );
}