import React from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { Link2, X, Check } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

interface ClipboardPromptProps {
  visible: boolean;
  url: string;
  onAccept: () => void;
  onDismiss: () => void;
}

export function ClipboardPrompt({ visible, url, onAccept, onDismiss }: ClipboardPromptProps) {
  const theme = useTheme();

  const truncateUrl = (url: string, maxLength: number = 50) => {
    if (url.length <= maxLength) return url;
    return url.substring(0, maxLength) + '...';
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    modal: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      width: '100%',
      maxWidth: 400,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    icon: {
      marginRight: theme.spacing.sm,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    subtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
      lineHeight: 20,
    },
    urlContainer: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    url: {
      fontSize: 14,
      color: theme.colors.primary,
      fontFamily: 'monospace',
    },
    buttons: {
      flexDirection: 'row',
      gap: theme.spacing.sm,
    },
    button: {
      flex: 1,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
    },
    acceptButton: {
      backgroundColor: theme.colors.primary,
    },
    dismissButton: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '500',
      marginLeft: theme.spacing.xs,
    },
    acceptButtonText: {
      color: '#FFFFFF',
    },
    dismissButtonText: {
      color: theme.colors.text,
    },
  });

  return (
    <Modal visible={visible} animationType="fade" transparent>
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Link2 size={24} color={theme.colors.primary} style={styles.icon} />
            <Text style={styles.title}>Link Detected</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onDismiss}>
              <X size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <Text style={styles.subtitle}>
            We detected a link in your clipboard. Would you like to save it?
          </Text>

          <View style={styles.urlContainer}>
            <Text style={styles.url}>{truncateUrl(url)}</Text>
          </View>

          <View style={styles.buttons}>
            <TouchableOpacity
              style={[styles.button, styles.dismissButton]}
              onPress={onDismiss}
            >
              <X size={16} color={theme.colors.text} />
              <Text style={[styles.buttonText, styles.dismissButtonText]}>No</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.acceptButton]}
              onPress={onAccept}
            >
              <Check size={16} color="#FFFFFF" />
              <Text style={[styles.buttonText, styles.acceptButtonText]}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}