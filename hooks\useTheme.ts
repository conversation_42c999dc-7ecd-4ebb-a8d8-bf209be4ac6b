import { useColorScheme } from 'react-native';
import { useMemo } from 'react';

export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    card: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
  };
}

export function useTheme(): Theme {
  const colorScheme = useColorScheme();
  
  const theme = useMemo(() => {
    const isDark = colorScheme === 'dark';
    
    return {
      colors: {
        primary: '#3B82F6',
        secondary: '#14B8A6',
        accent: '#F97316',
        background: isDark ? '#000000' : '#FFFFFF',
        surface: isDark ? '#1a1a1a' : '#F8F9FA',
        card: isDark ? '#2a2a2a' : '#FFFFFF',
        text: isDark ? '#FFFFFF' : '#1F2937',
        textSecondary: isDark ? '#9CA3AF' : '#6B7280',
        border: isDark ? '#374151' : '#E5E7EB',
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
      },
      spacing: {
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
      },
      borderRadius: {
        sm: 8,
        md: 12,
        lg: 16,
      },
    };
  }, [colorScheme]);
  
  return theme;
}