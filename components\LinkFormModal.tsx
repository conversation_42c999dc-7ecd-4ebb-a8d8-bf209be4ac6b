import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { X, Save, Tag } from 'lucide-react-native';
import { SavedLink, LinkCategory, LinkFormData } from '@/types/link';
import { useTheme } from '@/hooks/useTheme';

interface LinkFormModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (data: LinkFormData) => Promise<void>;
  initialData?: SavedLink | null;
  initialUrl?: string;
}

const categories: { value: LinkCategory; label: string }[] = [
  { value: 'work', label: 'Work' },
  { value: 'personal', label: 'Personal' },
  { value: 'social', label: 'Social Media' },
  { value: 'shopping', label: 'Shopping' },
  { value: 'custom', label: 'Custom' },
];

export function LinkFormModal({
  visible,
  onClose,
  onSave,
  initialData,
  initialUrl,
}: LinkFormModalProps) {
  const theme = useTheme();
  const [formData, setFormData] = useState<LinkFormData>({
    url: '',
    title: '',
    description: '',
    category: 'personal',
    tags: [],
  });
  const [tagInput, setTagInput] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (initialData) {
        setFormData({
          url: initialData.url,
          title: initialData.title,
          description: initialData.description,
          category: initialData.category,
          tags: initialData.tags,
        });
      } else if (initialUrl) {
        setFormData(prev => ({ ...prev, url: initialUrl }));
        // Auto-generate title from URL
        try {
          const domain = new URL(initialUrl).hostname;
          setFormData(prev => ({ ...prev, title: domain }));
        } catch {
          // Ignore if URL is invalid
        }
      }
    }
  }, [visible, initialData, initialUrl]);

  const handleSave = async () => {
    if (!formData.url.trim()) {
      Alert.alert('Error', 'URL is required');
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
      resetForm();
    } catch (error) {
      Alert.alert('Error', 'Failed to save link');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      url: '',
      title: '',
      description: '',
      category: 'personal',
      tags: [],
    });
    setTagInput('');
  };

  const addTag = () => {
    const tag = tagInput.trim().toLowerCase();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({ ...prev, tags: [...prev.tags, tag] }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modal: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: theme.borderRadius.lg,
      borderTopRightRadius: theme.borderRadius.lg,
      maxHeight: '90%',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    title: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    content: {
      padding: theme.spacing.lg,
    },
    inputGroup: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.md,
      fontSize: 16,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    textArea: {
      height: 80,
      textAlignVertical: 'top',
    },
    categoryContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
    },
    categoryButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    categoryButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    categoryText: {
      color: theme.colors.text,
      fontSize: 14,
    },
    categoryTextActive: {
      color: '#FFFFFF',
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.xs,
      marginBottom: theme.spacing.sm,
    },
    tag: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagText: {
      color: '#FFFFFF',
      fontSize: 12,
      marginRight: theme.spacing.xs,
    },
    tagRemove: {
      padding: 2,
    },
    tagInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      fontSize: 14,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      marginRight: theme.spacing.sm,
    },
    addTagButton: {
      backgroundColor: theme.colors.secondary,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: theme.spacing.lg,
    },
    saveButtonDisabled: {
      backgroundColor: theme.colors.textSecondary,
    },
    saveButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: theme.spacing.sm,
    },
  });

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {initialData ? 'Edit Link' : 'Save Link'}
            </Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>URL *</Text>
              <TextInput
                style={styles.input}
                value={formData.url}
                onChangeText={url => setFormData(prev => ({ ...prev, url }))}
                placeholder="https://example.com"
                placeholderTextColor={theme.colors.textSecondary}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Title</Text>
              <TextInput
                style={styles.input}
                value={formData.title}
                onChangeText={title => setFormData(prev => ({ ...prev, title }))}
                placeholder="Enter a title for this link"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.description}
                onChangeText={description => setFormData(prev => ({ ...prev, description }))}
                placeholder="Add a description (optional)"
                placeholderTextColor={theme.colors.textSecondary}
                multiline
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Category</Text>
              <View style={styles.categoryContainer}>
                {categories.map(category => (
                  <TouchableOpacity
                    key={category.value}
                    style={[
                      styles.categoryButton,
                      formData.category === category.value && styles.categoryButtonActive,
                    ]}
                    onPress={() => setFormData(prev => ({ ...prev, category: category.value }))}
                  >
                    <Text
                      style={[
                        styles.categoryText,
                        formData.category === category.value && styles.categoryTextActive,
                      ]}
                    >
                      {category.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tags</Text>
              {formData.tags.length > 0 && (
                <View style={styles.tagsContainer}>
                  {formData.tags.map(tag => (
                    <View key={tag} style={styles.tag}>
                      <Text style={styles.tagText}>{tag}</Text>
                      <TouchableOpacity
                        style={styles.tagRemove}
                        onPress={() => removeTag(tag)}
                      >
                        <X size={12} color="#FFFFFF" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
              <View style={styles.tagInputContainer}>
                <TextInput
                  style={styles.tagInput}
                  value={tagInput}
                  onChangeText={setTagInput}
                  placeholder="Add tags..."
                  placeholderTextColor={theme.colors.textSecondary}
                  onSubmitEditing={addTag}
                  autoCapitalize="none"
                />
                <TouchableOpacity style={styles.addTagButton} onPress={addTag}>
                  <Tag size={16} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.saveButton, loading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={loading}
            >
              <Save size={20} color="#FFFFFF" />
              <Text style={styles.saveButtonText}>
                {loading ? 'Saving...' : 'Save Link'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}