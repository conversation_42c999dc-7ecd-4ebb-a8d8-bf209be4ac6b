import React, { useState } from 'react';
import { View, FlatList, StyleSheet, SafeAreaView, RefreshControl } from 'react-native';
import { useLinks } from '@/hooks/useLinks';
import { useTheme } from '@/hooks/useTheme';
import { LinkCard } from '@/components/LinkCard';
import { LinkFormModal } from '@/components/LinkFormModal';
import { SearchBar } from '@/components/SearchBar';
import { EmptyState } from '@/components/EmptyState';
import { SavedLink, LinkFormData } from '@/types/link';

export default function SocialLinksScreen() {
  const theme = useTheme();
  const {
    getLinksByCategory,
    updateLink,
    deleteLink,
    toggleFavorite,
    incrementAccessCount,
    searchLinks,
  } = useLinks();
  
  const [showLinkForm, setShowLinkForm] = useState(false);
  const [editingLink, setEditingLink] = useState<SavedLink | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  const socialLinks = getLinksByCategory('social');
  const filteredLinks = searchQuery ? 
    searchLinks(searchQuery).filter(link => link.category === 'social') : 
    socialLinks;

  const handleSaveLink = async (data: LinkFormData) => {
    if (editingLink) {
      await updateLink(editingLink.id, data);
    }
    setShowLinkForm(false);
    setEditingLink(null);
  };

  const handleEditLink = (link: SavedLink) => {
    setEditingLink(link);
    setShowLinkForm(true);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <SearchBar 
          onSearch={setSearchQuery} 
          placeholder="Search social links..."
        />
        
        <FlatList
          data={filteredLinks}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <LinkCard
              link={item}
              onToggleFavorite={toggleFavorite}
              onDelete={deleteLink}
              onEdit={handleEditLink}
              onAccess={incrementAccessCount}
            />
          )}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={
            <EmptyState 
              type={searchQuery ? 'no-search' : 'no-links'}
              message={searchQuery ? undefined : 'No social media links saved yet. Copy a social link to get started!'}
            />
          }
        />
      </View>

      <LinkFormModal
        visible={showLinkForm}
        onClose={() => {
          setShowLinkForm(false);
          setEditingLink(null);
        }}
        onSave={handleSaveLink}
        initialData={editingLink}
      />
    </SafeAreaView>
  );
}