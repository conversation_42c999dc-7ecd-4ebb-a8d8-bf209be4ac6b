import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Link2, Search, Star } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

interface EmptyStateProps {
  type: 'no-links' | 'no-search' | 'no-favorites';
  message?: string;
}

export function EmptyState({ type, message }: EmptyStateProps) {
  const theme = useTheme();

  const getConfig = () => {
    switch (type) {
      case 'no-links':
        return {
          icon: Link2,
          title: 'No Links Yet',
          subtitle: message || 'Copy a link anywhere and we\'ll help you save it!',
        };
      case 'no-search':
        return {
          icon: Search,
          title: 'No Results Found',
          subtitle: message || 'Try adjusting your search terms.',
        };
      case 'no-favorites':
        return {
          icon: Star,
          title: 'No Favorites',
          subtitle: message || 'Star links to add them to your favorites.',
        };
      default:
        return {
          icon: Link2,
          title: 'Empty',
          subtitle: '',
        };
    }
  };

  const config = getConfig();
  const IconComponent = config.icon;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.xl,
      paddingVertical: theme.spacing.xl * 2,
    },
    iconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.surface,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.spacing.lg,
    },
    title: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <IconComponent size={32} color={theme.colors.textSecondary} />
      </View>
      <Text style={styles.title}>{config.title}</Text>
      <Text style={styles.subtitle}>{config.subtitle}</Text>
    </View>
  );
}