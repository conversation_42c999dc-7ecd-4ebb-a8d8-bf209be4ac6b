import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SavedLink, LinkCategory, LinkFormData } from '@/types/link';

const STORAGE_KEY = 'saved_links';

export function useLinks() {
  const [links, setLinks] = useState<SavedLink[]>([]);
  const [loading, setLoading] = useState(true);

  // Load links from storage
  useEffect(() => {
    loadLinks();
  }, []);

  const loadLinks = async () => {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedLinks = JSON.parse(stored).map((link: any) => ({
          ...link,
          createdAt: new Date(link.createdAt),
          lastAccessedAt: link.lastAccessedAt ? new Date(link.lastAccessedAt) : undefined,
        }));
        setLinks(parsedLinks);
      }
    } catch (error) {
      console.error('Error loading links:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveLinks = async (newLinks: SavedLink[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newLinks));
      setLinks(newLinks);
    } catch (error) {
      console.error('Error saving links:', error);
    }
  };

  const addLink = async (linkData: LinkFormData): Promise<string> => {
    const newLink: SavedLink = {
      id: Date.now().toString(),
      url: linkData.url,
      title: linkData.title || 'Untitled Link',
      description: linkData.description || '',
      category: linkData.category,
      tags: linkData.tags,
      isFavorite: false,
      accessCount: 0,
      createdAt: new Date(),
      thumbnail: await generateThumbnail(linkData.url),
    };

    const updatedLinks = [newLink, ...links];
    await saveLinks(updatedLinks);
    return newLink.id;
  };

  const updateLink = async (id: string, updates: Partial<SavedLink>) => {
    const updatedLinks = links.map(link => 
      link.id === id ? { ...link, ...updates } : link
    );
    await saveLinks(updatedLinks);
  };

  const deleteLink = async (id: string) => {
    const updatedLinks = links.filter(link => link.id !== id);
    await saveLinks(updatedLinks);
  };

  const toggleFavorite = async (id: string) => {
    await updateLink(id, { 
      isFavorite: !links.find(link => link.id === id)?.isFavorite 
    });
  };

  const incrementAccessCount = async (id: string) => {
    const link = links.find(l => l.id === id);
    if (link) {
      await updateLink(id, {
        accessCount: link.accessCount + 1,
        lastAccessedAt: new Date(),
      });
    }
  };

  const getLinksByCategory = (category: LinkCategory) => {
    if (category === 'all') return links;
    return links.filter(link => link.category === category);
  };

  const getFavoriteLinks = () => {
    return links.filter(link => link.isFavorite);
  };

  const searchLinks = (query: string) => {
    if (!query.trim()) return links;
    const lowercaseQuery = query.toLowerCase();
    return links.filter(link => 
      link.title.toLowerCase().includes(lowercaseQuery) ||
      link.description.toLowerCase().includes(lowercaseQuery) ||
      link.url.toLowerCase().includes(lowercaseQuery) ||
      link.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  };

  const generateThumbnail = async (url: string): Promise<string | undefined> => {
    // In a real app, you would use a service to generate thumbnails
    // For demo purposes, we'll return a placeholder
    try {
      const domain = new URL(url).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    } catch {
      return undefined;
    }
  };

  return {
    links,
    loading,
    addLink,
    updateLink,
    deleteLink,
    toggleFavorite,
    incrementAccessCount,
    getLinksByCategory,
    getFavoriteLinks,
    searchLinks,
  };
}