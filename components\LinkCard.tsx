import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Linking, Alert } from 'react-native';
import { Star, ExternalLink, Share, Trash2, CreditCard as Edit3 } from 'lucide-react-native';
import { SavedLink } from '@/types/link';
import { useTheme } from '@/hooks/useTheme';

interface LinkCardProps {
  link: SavedLink;
  onToggleFavorite: (id: string) => void;
  onDelete: (id: string) => void;
  onEdit: (link: SavedLink) => void;
  onAccess: (id: string) => void;
}

export function LinkCard({ link, onToggleFavorite, onDelete, onEdit, onAccess }: LinkCardProps) {
  const theme = useTheme();

  const handlePress = async () => {
    try {
      onAccess(link.id);
      await Linking.openURL(link.url);
    } catch (error) {
      Alert.alert('Error', 'Could not open link');
    }
  };

  const handleShare = async () => {
    try {
      const { Share } = await import('react-native');
      await Share.share({
        message: `${link.title}\n${link.url}`,
        url: link.url,
      });
    } catch (error) {
      console.log('Error sharing:', error);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Link',
      'Are you sure you want to delete this link?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => onDelete(link.id) },
      ]
    );
  };

  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm,
    },
    thumbnail: {
      width: 48,
      height: 48,
      borderRadius: theme.borderRadius.sm,
      marginRight: theme.spacing.md,
      backgroundColor: theme.colors.surface,
    },
    content: {
      flex: 1,
    },
    title: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    url: {
      fontSize: 14,
      color: theme.colors.primary,
      marginBottom: 4,
    },
    description: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    footer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: theme.spacing.sm,
      paddingTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    categoryTag: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
    },
    categoryText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textTransform: 'capitalize',
    },
    actions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionButton: {
      padding: theme.spacing.xs,
      marginLeft: theme.spacing.sm,
    },
    stats: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: theme.spacing.xs,
    },
    statsText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.xs,
    },
  });

  return (
    <TouchableOpacity style={styles.card} onPress={handlePress} activeOpacity={0.7}>
      <View style={styles.header}>
        {link.thumbnail && (
          <Image source={{ uri: link.thumbnail }} style={styles.thumbnail} />
        )}
        <View style={styles.content}>
          <Text style={styles.title} numberOfLines={2}>
            {link.title}
          </Text>
          <Text style={styles.url} numberOfLines={1}>
            {link.url}
          </Text>
          {link.description && (
            <Text style={styles.description} numberOfLines={3}>
              {link.description}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.categoryTag}>
          <Text style={styles.categoryText}>{link.category}</Text>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onToggleFavorite(link.id)}
          >
            <Star
              size={20}
              color={link.isFavorite ? theme.colors.warning : theme.colors.textSecondary}
              fill={link.isFavorite ? theme.colors.warning : 'none'}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => onEdit(link)}>
            <Edit3 size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Share size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <Trash2 size={20} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      </View>

      {link.accessCount > 0 && (
        <View style={styles.stats}>
          <ExternalLink size={12} color={theme.colors.textSecondary} />
          <Text style={styles.statsText}>
            Opened {link.accessCount} times
          </Text>
          {link.lastAccessedAt && (
            <Text style={styles.statsText}>
              • Last: {link.lastAccessedAt.toLocaleDateString()}
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
}